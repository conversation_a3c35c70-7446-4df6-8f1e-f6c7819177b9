'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { axiosInstance } from '@/lib/axiosInstance';

const educationSchema = z.object({
  degree: z.string().min(1, 'Degree is required'),
  fieldOfStudy: z.string().min(1, 'Field of study is required'),
  institution: z.string().min(1, 'Institution name is required'),
  location: z.string().min(1, 'Location is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  grade: z.string().optional(),
  description: z.string().optional(),
});

type EducationFormData = z.infer<typeof educationSchema>;

interface AddEducationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  freelancerId: string;
  onSuccess?: () => void;
}

export default function AddEducationDialog({
  open,
  onOpenChange,
  freelancerId,
  onSuccess,
}: AddEducationDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<EducationFormData>({
    resolver: zodResolver(educationSchema),
    defaultValues: {
      degree: '',
      fieldOfStudy: '',
      institution: '',
      location: '',
      startDate: '',
      endDate: '',
      grade: '',
      description: '',
    },
  });

  const onSubmit = async (data: EducationFormData) => {
    setIsLoading(true);
    try {
      await axiosInstance.post(`/freelancer/${freelancerId}/education`, {
        degree: data.degree,
        fieldOfStudy: data.fieldOfStudy,
        institution: data.institution,
        location: data.location,
        startDate: data.startDate,
        endDate: data.endDate || undefined,
        grade: data.grade || undefined,
        description: data.description || undefined,
      });

      toast({
        title: 'Success',
        description: 'Education added successfully!',
      });

      form.reset();
      onOpenChange(false);
      onSuccess?.();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to add education',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-black text-white border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white">Add Education</DialogTitle>
          <DialogDescription className="text-gray-400">
            Add your educational background to showcase your qualifications.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="degree"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Degree</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-gray-800 text-white border-gray-600">
                          <SelectValue placeholder="Select degree" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-gray-800 text-white border-gray-600">
                        <SelectItem
                          value="High School"
                          className="hover:bg-gray-700"
                        >
                          High School
                        </SelectItem>
                        <SelectItem
                          value="Associate"
                          className="hover:bg-gray-700"
                        >
                          Associate
                        </SelectItem>
                        <SelectItem
                          value="Bachelor's"
                          className="hover:bg-gray-700"
                        >
                          Bachelor's
                        </SelectItem>
                        <SelectItem
                          value="Master's"
                          className="hover:bg-gray-700"
                        >
                          Master's
                        </SelectItem>
                        <SelectItem value="PhD" className="hover:bg-gray-700">
                          PhD
                        </SelectItem>
                        <SelectItem
                          value="Certificate"
                          className="hover:bg-gray-700"
                        >
                          Certificate
                        </SelectItem>
                        <SelectItem
                          value="Diploma"
                          className="hover:bg-gray-700"
                        >
                          Diploma
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fieldOfStudy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Field of Study</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Computer Science"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="institution"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Institution</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., University of Technology"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Location</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., New York, NY"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Start Date</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">
                      End Date (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="grade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">
                      Grade/GPA (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 3.8 GPA or First Class"
                        {...field}
                        className="bg-gray-800 text-white border-gray-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">
                    Description (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe relevant coursework, achievements, or activities..."
                      rows={3}
                      {...field}
                      className="bg-gray-800 text-white border-gray-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="bg-gray-800 text-white border-gray-600 hover:bg-gray-700"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? 'Adding...' : 'Add Education'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
