"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/profiles/page",{

/***/ "(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx":
/*!***********************************************************!*\
  !*** ./src/components/dialogs/ProjectSelectionDialog.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProjectSelectionDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Github,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Github,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Github,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Github,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProjectSelectionDialog(param) {\n    let { open, onOpenChange, freelancerId, currentProfileId, onSuccess } = param;\n    _s();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProjects, setSelectedProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [existingProjectIds, setExistingProjectIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddingProjects, setIsAddingProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (open && freelancerId && currentProfileId) {\n            fetchProjects();\n            fetchCurrentProfileProjects();\n        }\n    }, [\n        open,\n        freelancerId,\n        currentProfileId\n    ]);\n    const fetchProjects = async ()=>{\n        setIsLoading(true);\n        try {\n            var _response_data_data, _response_data;\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_7__.axiosInstance.get(\"/freelancer/\".concat(freelancerId));\n            const projectsData = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.projects;\n            if (projectsData && typeof projectsData === \"object\") {\n                // Convert projects object to array\n                const projectsArray = Object.values(projectsData);\n                setProjects(projectsArray);\n            } else {\n                setProjects([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching projects:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load projects\",\n                variant: \"destructive\"\n            });\n            setProjects([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchCurrentProfileProjects = async ()=>{\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_7__.axiosInstance.get(\"/freelancer/profile/\".concat(currentProfileId));\n            const currentProfile = response.data.data;\n            const projectIds = (currentProfile.projects || []).map((p)=>p._id || p.id || p);\n            setExistingProjectIds(projectIds);\n        } catch (error) {\n            console.error(\"Error fetching current profile projects:\", error);\n            setExistingProjectIds([]);\n        }\n    };\n    const handleProjectToggle = (projectId)=>{\n        setSelectedProjects((prev)=>prev.includes(projectId) ? prev.filter((id)=>id !== projectId) : [\n                ...prev,\n                projectId\n            ]);\n    };\n    const handleAddProjects = async ()=>{\n        if (selectedProjects.length === 0) {\n            toast({\n                title: \"No Selection\",\n                description: \"Please select at least one project to add\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsAddingProjects(true);\n        try {\n            // First, get the current profile to see existing projects\n            const profileResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_7__.axiosInstance.get(\"/freelancer/profile/\".concat(currentProfileId));\n            const currentProfile = profileResponse.data.data;\n            // Get existing project IDs\n            const existingProjectIds = (currentProfile.projects || []).map((p)=>p._id || p.id || p);\n            // Combine existing projects with newly selected ones (avoid duplicates)\n            const allProjectIds = [\n                ...new Set([\n                    ...existingProjectIds,\n                    ...selectedProjects\n                ])\n            ];\n            // Update the profile with the combined projects array\n            const updateResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_7__.axiosInstance.put(\"/freelancer/profile/\".concat(currentProfileId), {\n                projects: allProjectIds\n            });\n            toast({\n                title: \"Success\",\n                description: \"\".concat(selectedProjects.length, \" project(s) added to profile successfully!\")\n            });\n            setSelectedProjects([]);\n            onOpenChange(false);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"Error adding projects:\", error);\n            console.error(\"Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            toast({\n                title: \"Error\",\n                description: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to add projects to profile\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsAddingProjects(false);\n        }\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"N/A\";\n        return new Date(dateString).toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[900px] max-h-[80vh] overflow-y-auto bg-black text-white border-gray-600\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"text-white\",\n                            children: \"Select Projects for Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Choose from your existing projects to add to this profile. You can select multiple projects.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Loading projects...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, this) : projects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"No projects found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Add projects from the Projects page first to select them for your profile.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mt-2\",\n                                children: [\n                                    \"Debug: Freelancer ID: \",\n                                    freelancerId,\n                                    \", Profile ID:\",\n                                    \" \",\n                                    currentProfileId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: projects.map((project)=>{\n                                    const isAlreadyInProfile = existingProjectIds.includes(project._id);\n                                    const isSelected = selectedProjects.includes(project._id);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"transition-all duration-200 \".concat(isAlreadyInProfile ? \"bg-green-900/30 border-green-600 opacity-60\" : isSelected ? \"bg-blue-900/50 border-blue-500 cursor-pointer\" : \"bg-gray-800 border-gray-600 hover:bg-gray-700 cursor-pointer\"),\n                                        onClick: ()=>!isAlreadyInProfile && handleProjectToggle(project._id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"text-white text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    project.projectName,\n                                                                    isAlreadyInProfile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        className: \"bg-green-600 hover:bg-green-600 text-xs\",\n                                                                        children: \"Already Added\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 31\n                                                                    }, this) : isSelected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 31\n                                                                    }, this) : null\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            project.githubLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: project.githubLink,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                onClick: (e)=>e.stopPropagation(),\n                                                                className: \"text-gray-400 hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"\".concat(project.verified ? \"bg-green-600 hover:bg-green-600\" : \"bg-yellow-600 hover:bg-yellow-600\"),\n                                                                children: project.verified ? \"VERIFIED\" : \"PENDING\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            project.projectType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-gray-300 border-gray-500\",\n                                                                children: project.projectType\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-sm line-clamp-3\",\n                                                        children: project.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    project.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Role:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            project.role\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-sm text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    formatDate(project.start),\n                                                                    \" -\",\n                                                                    \" \",\n                                                                    formatDate(project.end)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    project.techUsed && project.techUsed.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            project.techUsed.slice(0, 3).map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs bg-gray-700 text-gray-300\",\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 31\n                                                                }, this)),\n                                                            project.techUsed.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs bg-gray-700 text-gray-300\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    project.techUsed.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, project._id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center pt-4 border-t border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    selectedProjects.length,\n                                                    \" project(s) selected\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this),\n                                            existingProjectIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    existingProjectIds.length,\n                                                    \" project(s) already in profile\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>onOpenChange(false),\n                                                className: \"bg-gray-800 text-white border-gray-600 hover:bg-gray-700\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleAddProjects,\n                                                disabled: selectedProjects.length === 0 || isAddingProjects,\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Github_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isAddingProjects ? \"Adding...\" : \"Add \".concat(selectedProjects.length, \" Project(s)\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\dialogs\\\\ProjectSelectionDialog.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectSelectionDialog, \"0BKExN41TFwbCmGqSsDoE6OX4so=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = ProjectSelectionDialog;\nvar _c;\n$RefreshReg$(_c, \"ProjectSelectionDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dialogs/ProjectSelectionDialog.tsx\n"));

/***/ })

});